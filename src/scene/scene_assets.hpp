#ifndef __IF__SCENE_ASSETS_HPP
#define __IF__SCENE_ASSETS_HPP

// C standard library
#include <cstddef>

// C++ standard library
#include <functional>
#include <iostream>
#include <string>
#include <tuple>
#include <unordered_set>

// Local includes
#include "../assets/assets_loader.hpp"
#include "../assets/assets_manifest.hpp"
#include "../renderer/renderer_events.hpp"
#include "../services/service_locator.hpp"
#include "../utils/string_id.hpp"
#include "scene_context.hpp"

namespace std {
  template <>
  struct hash<std::tuple<IronFrost::StringID, IronFrost::ResourceType>> {
      std::size_t operator()(const std::tuple<IronFrost::StringID, IronFrost::ResourceType>& key) const {
          const auto& [id, type] = key;
          std::size_t h1 = std::hash<IronFrost::StringID>{}(id);
          std::size_t h2 = std::hash<IronFrost::ResourceType>{}(type);
          return h1 ^ (h2 << 1);
      }
  };
}

namespace IronFrost {
  class SceneAssets {
    private:
      EventListenerHandle m_resourceCreatedHandle;
      
      SceneContext& m_sceneContext;
      EventDispatcher& m_eventDispatcher;
      
      std::unique_ptr<AssetsManifest> m_assetsManifest{nullptr};
      std::unique_ptr<AssetsLoader> m_assetsLoader{nullptr};
      
      std::unordered_set<std::tuple<StringID, ResourceType>> m_loadedAssets{};
      size_t m_totalAssetsToLoad{0};

    public:
      SceneAssets(SceneContext& sceneContext) : 
        m_sceneContext(sceneContext),
        m_eventDispatcher(ServiceLocator::getService<EventDispatcher>())
      {
        m_assetsManifest = std::make_unique<AssetsManifest>(sceneContext.vfs);
        m_assetsLoader = std::make_unique<AssetsLoader>(sceneContext.assetsManager);
      }

      void loadManifest(const std::string& path) {
        m_assetsManifest->loadFromFile(path);
        m_totalAssetsToLoad = m_assetsManifest->size();

        m_resourceCreatedHandle = m_eventDispatcher.registerListener<ResourceCreatedEvent>(
          [&](const ResourceCreatedEvent& event) {
            if (m_assetsManifest->contains(event.name())) {
              m_loadedAssets.insert(std::make_tuple(event.name(), event.type())); 
              std::cout << "Scene Resource Loaded: " << StringID::getString(event.name()) << std::endl;
            }
          }
        );
      }

      void loadNextAsset() {
        if (m_assetsManifest->empty()) {
          return;
        }

        auto assetInfo = m_assetsManifest->takeNext();

        std::cout << "Loading scene asset: " << assetInfo.config["name"] << std::endl;
        m_assetsLoader->loadAsync(assetInfo);
      }

      void unload() {
        m_eventDispatcher.unregisterListener<ResourceCreatedEvent>(m_resourceCreatedHandle);

        for (auto& [name, type] : m_loadedAssets) {
          m_eventDispatcher.dispatch<DestroyResourceEvent>(name, type);
        }
      }

      bool allAssetsLoaded() const {
        return m_assetsManifest->empty() && m_loadedAssets.size() == m_totalAssetsToLoad;
      }

      float progress() const {
        return static_cast<float>(m_loadedAssets.size()) / m_totalAssetsToLoad;
      }
  };
}

#endif
