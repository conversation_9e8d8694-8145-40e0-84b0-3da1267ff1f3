#include "collision_sphere.hpp"
#include "collision_aabb.hpp"
#include "collision_plane.hpp"

namespace IronFrost {
  void CollisionSphere::update(const glm::mat4& transform) {
    m_worldSphere = CollisionMath::transformSphere(m_localSphere, transform);
  }

  CollisionMath::Sphere CollisionSphere::getWorldSphere() const {
    return m_worldSphere;
  }

  CollisionMath::AABB CollisionSphere::getWorldAABB() const {
    return CollisionMath::AABB::fromCenterAndSize(m_worldSphere.center, glm::vec3(m_worldSphere.radius * 2.0f));
  }

  bool CollisionSphere::intersects(const CollisionShape& other) const {
    return other.intersectsSphere(*this);
  }

  bool CollisionSphere::intersectsSphere(const CollisionSphere& sphere) const {
    return CollisionMath::sphereIntersection(m_worldSphere, sphere.getWorldSphere());
  }

  bool CollisionSphere::intersectsAABB(const CollisionAABB& aabb) const {
    return CollisionMath::sphereAABBIntersection(m_worldSphere, aabb.getWorldAABB());
  }

  bool CollisionSphere::intersectsPlane(const CollisionPlane& plane) const {
    return CollisionMath::spherePlaneIntersection(m_worldSphere, plane.getWorldPlane());
  }

  glm::vec3 CollisionSphere::resolveSphereCollision(const CollisionMath::Sphere& sphere) {
    return CollisionMath::resolveSphereSphereCollision(sphere, m_worldSphere);
  }

  glm::vec3 CollisionSphere::resolveAABBCollision(const CollisionMath::AABB& aabb) {
    return CollisionMath::resolveAABBSphereCollision(aabb, m_worldSphere);
  }
}
