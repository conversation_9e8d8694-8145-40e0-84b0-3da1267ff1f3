#include "application.hpp"

// C++ standard library
#include <iostream>
#include <memory>
#include <sstream>
#include <string>
#include <utility>

// Local includes
#include "../assets/assets_manager.hpp"
#include "../audio/audio_engine.hpp"
#include "config/config.hpp"
#include "console_manager.hpp"
#include "../gui/gui.hpp"
#include "../renderer/gui_renderer.hpp"
#include "../renderer/renderer.hpp"
#include "../scene/loaders/scene_loader.hpp"
#include "../scene/scene_manager.hpp"
#include "../scripts/script_engine.hpp"
#include "../services/service_locator.hpp"
#include "system_initializer.hpp"
#include "../utils/game_context.hpp"
#include "../vfs/vfs.hpp"
#include "../window/console/console.hpp"
#include "../window/window.hpp"

namespace IronFrost {
  Application::Application() {
    // Constructor is now simple - ApplicationBuilder handles initialization
    SystemInitializer::registerServices();
  }

  Application::~Application() {
    std::cout << "Closing application..." << '\n';

    // Explicit cleanup in correct order to avoid static destruction order issues
    // Clear keyboard callbacks before script engine to avoid Lua callback issues
    if (m_window) {
      std::cout << "Clearing keyboard callbacks..." << '\n';
      m_window->getKeyboard().clearAllCallbacks();
    }

    // Clear script engine first to unregister event listeners before EventDispatcher is destroyed
    if (m_scriptEngine) {
      std::cout << "Clearing script engine..." << '\n';
      m_scriptEngine.reset();
    }

    // Clear scene manager after script engine to avoid Lua callback issues
    if (m_sceneManager) {
      std::cout << "Clearing scene manager..." << '\n';
      m_sceneManager.reset();
    }
  };

  // initialize() method removed - ApplicationBuilder handles all initialization

  void Application::run() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    while (!m_window->shouldClose()) {
      eventDispatcher.processEvents();
      
      m_deltaTimeCalculator.update(
        [&](double deltaTime) {
          m_scriptEngine->executePerFrame(static_cast<float>(deltaTime));
          m_sceneManager->executeScenePerFrameScripts(static_cast<float>(deltaTime));
          
          m_sceneManager->updateCurrentScene(static_cast<float>(deltaTime), *m_gameContext);
        });

      m_renderer->beginFrame();
      m_sceneManager->renderCurrentScene();
      m_renderer->getGUIRenderer().render(*m_globalGUI);
      m_renderer->endFrame();

      m_window->swapBuffers();
      m_window->pollEvents();
    }
  }

  IWindow& Application::getWindow() {
    return *m_window;
  }

  IVFS& Application::getVFS() {
    return *m_vfs;
  }

  Console& Application::getConsole() {
    return *m_console;
  }

  IAudioEngine& Application::getAudioEngine() {
    return *m_audioEngine;
  }
}
