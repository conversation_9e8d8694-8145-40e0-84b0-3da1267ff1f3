#ifndef __IF__SCENE_DATA_STORAGE_HPP
#define __IF__SCENE_DATA_STORAGE_HPP

// C++ standard library
#include <memory>
#include <iostream>

// Local includes
#include "../utils/containers_set.hpp"
#include "../utils/collision_math.hpp"
#include "../services/service_locator.hpp"
#include "../events/event_dispatcher.hpp"

namespace IronFrost {
  class SceneDataStorage {
    private:
      ValueContainersSet<CollisionMath::AABB> m_sceneDataStorage;

    public:
      SceneDataStorage() {
        EventDispatcher& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

        eventDispatcher.registerListener<LoadMeshEvent>([&](const LoadMeshEvent& event) {
          m_sceneDataStorage.getContainer<CollisionMath::AABB>().insert(event.name(), event.meshData().bounds);
        });

        eventDispatcher.registerListener<LoadModelEvent>([&](const LoadModelEvent& event) {
          m_sceneDataStorage.getContainer<CollisionMath::AABB>().insert(event.name(), event.modelData().bounds);
        });

        eventDispatcher.registerListener<LoadTerrainEvent>([&](const LoadTerrainEvent& event) {
          m_sceneDataStorage.getContainer<CollisionMath::AABB>().insert(event.name(), event.terrainData().meshData.bounds);
        });
      };
      
      template<typename T>
      T& get(const StringID& name) {
        return m_sceneDataStorage.getContainer<T>().get(name);
      }

      template<typename T>
      const T& get(const StringID& name) const {
        return m_sceneDataStorage.getContainer<T>().get(name);
      }
      
      ~SceneDataStorage() = default;
  };
}

#endif
