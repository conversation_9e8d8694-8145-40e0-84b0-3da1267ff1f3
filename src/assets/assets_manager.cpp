#include "assets_manager.hpp"

// C++ standard library
#include <exception>
#include <iostream>
#include <memory>
#include <stdexcept>
#include <string>
#include <utility>

// Local includes
#include "asset_data_types.hpp"
#include "loaders/audio_loader.hpp"
#include "loaders/font_loader.hpp"
#include "loaders/image_loader.hpp"
#include "loaders/mesh_loader.hpp"
#include "loaders/model_loader.hpp"
#include "loaders/shader_loader.hpp"
#include "loaders/heightmap_loader.hpp"
#include "generators/terrain_generator.hpp"

namespace IronFrost {
  AssetsManager::AssetsManager(IVFS &vfs) : m_vfs(vfs) {}

  std::unique_ptr<ShaderData> AssetsManager::loadShader(const std::string& path) {
    ShaderLoader shaderLoader(m_vfs);
    return shaderLoader.loadShader(path);
  }

  const ShaderData& AssetsManager::loadShader(const StringID& name, const std::string& path) {
    std::unique_ptr<ShaderData> shaderData = loadShader(path);
    insert(name, std::move(shaderData));

    return get<ShaderData>(name);
  }
  
  std::unique_ptr<MeshData> AssetsManager::createPrimitive(PrimitiveType type, const PrimitiveParams& params) {
    MeshLoader meshLoader(m_vfs);
    return meshLoader.createPrimitive(type, params);
  }

  const MeshData& AssetsManager::createPrimitive(const StringID& name, PrimitiveType type, const PrimitiveParams& params) {
    if (has<MeshData>(name)) {
      throw std::runtime_error("Mesh already exists: " + StringID::getString(name));
    }

    std::unique_ptr<MeshData> meshData = createPrimitive(type, params);
    insert(name, std::move(meshData));

    return get<MeshData>(name);
  }

  std::unique_ptr<ModelData> AssetsManager::loadModel(const std::string& path) {
    ModelLoader modelLoader(m_vfs);
    return modelLoader.loadModel(path);
  }

  const ModelData& AssetsManager::loadModel(const StringID& name, const std::string& path) {
    if (has<ModelData>(name)) {
      throw std::runtime_error("Model already exists: " + StringID::getString(name));
    }

    std::unique_ptr<ModelData> modelData = loadModel(path);
    insert(name, std::move(modelData));

    return get<ModelData>(name);
  }

  std::unique_ptr<ImageData> AssetsManager::loadImage(const std::string& path) {
    ImageLoader imageLoader(m_vfs);
    return imageLoader.loadImage(path);
  }

  const ImageData& AssetsManager::loadImage(const StringID& name, const std::string& path) {
    if (has<ImageData>(name)) {
      throw std::runtime_error("Image already exists: " + StringID::getString(name));
    }

    std::unique_ptr<ImageData> imageData = loadImage(path);
    insert(name, std::move(imageData));

    return get<ImageData>(name);
  }

  std::unique_ptr<HeightmapData> AssetsManager::loadHeightmap(const std::string& path) {
    HeightmapLoader heightmapLoader(m_vfs);
    return heightmapLoader.loadHeightmap(path);
  }

  const HeightmapData& AssetsManager::loadHeightmap(const StringID& name, const std::string& path) {
    if (has<HeightmapData>(name)) {
      throw std::runtime_error("Heightmap already exists: " + StringID::getString(name));
    }

    std::unique_ptr<HeightmapData> heightmapData = loadHeightmap(path);
    insert(name, std::move(heightmapData));

    return get<HeightmapData>(name);
  }

  std::unique_ptr<TerrainData> AssetsManager::loadTerrain(const TerrainParams& params) {
    auto heightmapData = loadHeightmap(params.heightmapPath);
    
    TerrainGenerator terrainGenerator;
    return terrainGenerator.generateTerrain(*heightmapData, params.heightScale, params.blockSize);
  }

  const TerrainData& AssetsManager::loadTerrain(const StringID& name, const TerrainParams& params) {
    if (has<TerrainData>(name)) {
      throw std::runtime_error("Terrain already exists: " + StringID::getString(name));
    }

    std::unique_ptr<TerrainData> terrainData = loadTerrain(params);
    insert(name, std::move(terrainData));

    return get<TerrainData>(name);
  }

  std::unique_ptr<AudioData> AssetsManager::loadAudio(const std::string& path) {
    AudioLoader audioLoader(m_vfs);
    return audioLoader.loadAudioFile(path);
  }

  const AudioData& AssetsManager::loadAudio(const StringID& name, const std::string& path) {
    if (has<AudioData>(name)) {
      throw std::runtime_error("Audio already exists: " + StringID::getString(name));
    }

    std::unique_ptr<AudioData> audioData = loadAudio(path);
    insert(name, std::move(audioData));

    return get<AudioData>(name);
  }

  std::unique_ptr<FontData> AssetsManager::loadFont(const std::string& path, unsigned int size) {
    FontLoader fontLoader(m_vfs);
    return fontLoader.loadFont(path, size);
  }

  const FontData& AssetsManager::loadFont(const StringID& name, const std::string& path, unsigned int size) {
    if (has<FontData>(name)) {
      throw std::runtime_error("Font already exists: " + StringID::getString(name));
    }

    std::unique_ptr<FontData> fontData = loadFont(path, size);
    insert(name, std::move(fontData));

    return get<FontData>(name);
  }
}
