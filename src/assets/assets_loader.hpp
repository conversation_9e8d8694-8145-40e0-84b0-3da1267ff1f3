#ifndef __IF__ASSETS_LOADER_HPP
#define __IF__ASSETS_LOADER_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "asset_types.hpp"
#include "asset_data_types.hpp"
#include "asset_primitive_types.hpp"
#include "../services/service_locator.hpp"
#include "../events/event_dispatcher.hpp"

using json = nlohmann::json;

namespace IronFrost {
  class IVFS;
  class AssetsManager;
  class AssetsFromFileLoader;
  class StringID;

  class AssetsLoader {
    private:
      AssetsManager& m_assetsManager;
      EventDispatcher& m_eventDispatcher;
      
      static const std::unordered_map<std::string, PrimitiveType> s_primitiveTypes;

      BlinnPhongMaterialData loadBlinnPhongMaterial(const json& materialConfig) const;
      PBRMaterialData loadPBRMaterial(const json& materialConfig) const;

      template<typename E, typename D>
      void dispatchEvent(const StringID& name, const D& data, bool async) {
        if (async) {
          m_eventDispatcher.dispatchAsync<E>(name, data);
        } else {
          m_eventDispatcher.dispatch<E>(name, data);
        }
      }

      template<typename E, typename D>
      void dispatchEvent(const StringID& name, D&& data, bool async) {
        if (async) {
          m_eventDispatcher.dispatchAsync<E>(name, std::forward<D>(data));
        } else {
          m_eventDispatcher.dispatch<E>(name, std::forward<D>(data));
        }
      }

      template<AssetType T>
      void load(const json& config, bool async);

      struct AssetTypeHash {
        size_t operator()(AssetType t) const noexcept {
          return static_cast<size_t>(t);
        }
      };
      
      using AssetTypeFn = void (AssetsLoader::*)(const json&, bool async);
      
      static const std::unordered_map<AssetType, AssetTypeFn, AssetTypeHash> s_dispatchMap;

    public:
      AssetsLoader(AssetsManager& assetsManager) : 
        m_assetsManager(assetsManager), 
        m_eventDispatcher(ServiceLocator::getService<EventDispatcher>()) {}

      void load(const AssetInfo& assetInfo);
      void loadAsync(const AssetInfo& assetInfo);
  };
}

#endif
