#include "system_initializer.hpp"

// C++ standard library
#include <iostream>
#include <memory>

// Local includes
#include "application.hpp"
#include "config/debug_config.hpp"
#include "../events/event_dispatcher.hpp"
#include "../events/events.hpp"
#include "../services/service_locator.hpp"
#include "../window/console/console.hpp"
#include "../window/window.hpp"

namespace IronFrost {

  void SystemInitializer::registerServices() {
    std::cout << "Registering core services" << '\n';
    
    std::unique_ptr<EventDispatcher> eventDispatcher = std::make_unique<EventDispatcher>();
    ServiceLocator::registerService<EventDispatcher>(std::move(eventDispatcher));

    std::unique_ptr<DebugConfig> debugConfig = std::make_unique<DebugConfig>();
    ServiceLocator::registerService<DebugConfig>(std::move(debugConfig));
  }

  void SystemInitializer::registerGlobalEventListeners(Application& application) {
    std::cout << "Registering global event listeners" << '\n';
    
    ServiceLocator::getService<EventDispatcher>().registerListener<KeyDownEvent>(
      [&application](const KeyDownEvent& event) {
        auto keyType = (KeyType)event.getKey();

        if (keyType == KEY_ESCAPE) {
          application.getWindow().close();
        }
        if (keyType == KEY_TILDE) {
          application.getConsole().toggle();
        }
      });
  }

}
