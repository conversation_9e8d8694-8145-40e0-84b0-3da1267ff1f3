#ifndef __IF__ASSET_EVENTS_HPP
#define __IF__ASSET_EVENTS_HPP

// C++ standard library
#include <string>

// Local includes
#include "../events/events.hpp"
#include "../utils/string_id.hpp"
#include "asset_data_types.hpp"

namespace IronFrost {
  class AssetLoadedEvent : public Event {
    public:
      std::string toString() const override {
        return "AssetLoadedEvent";
      }
  };

    class LoadMeshEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const MeshData& m_meshData;

    public:
      LoadMeshEvent(const StringID& name, const MeshData& meshData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_meshData(meshData)
      {}

      std::string toString() const override {
        return "LoadMeshEvent";
      }

      const StringID& name() const { return m_name; }
      const MeshData& meshData() const { return m_meshData; }
  };

  class LoadModelEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const ModelData& m_modelData;

    public:
      LoadModelEvent(const StringID& name, const ModelData& modelData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_modelData(modelData)
      {}

      std::string toString() const override {
        return "LoadModelEvent";
      }

      const StringID& name() const { return m_name; }
      const ModelData& modelData() const { return m_modelData; }
  };
  
  class LoadShaderEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const ShaderData& m_shaderData;

    public:
      LoadShaderEvent(const StringID& name, const ShaderData& shaderData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_shaderData(shaderData)
      {}

      std::string toString() const override {
        return "LoadShaderEvent";
      }

      const StringID& name() const { return m_name; }
      const ShaderData& shaderData() const { return m_shaderData; }
  };

  class LoadTextureEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const ImageData& m_imageData;

    public:
      LoadTextureEvent(const StringID& name, const ImageData& imageData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_imageData(imageData)
      {}

      std::string toString() const override {
        return "LoadTextureEvent";
      }

      const StringID& name() const { return m_name; }
      const ImageData& imageData() const { return m_imageData; }
  };

  class LoadHeightmapEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const HeightmapData& m_heightmapData;

    public:
      LoadHeightmapEvent(const StringID& name, const HeightmapData& heightmapData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_heightmapData(heightmapData)
      {}

      std::string toString() const override {
        return "LoadHeightmapEvent";
      }

      const StringID& name() const { return m_name; }
      const HeightmapData& heightmapData() const { return m_heightmapData; }
  };

  class LoadFontEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const FontData& m_fontData;

    public:
      LoadFontEvent(const StringID& name, const FontData& fontData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_fontData(fontData)
      {}

      std::string toString() const override {
        return "LoadFontEvent";
      }

      const StringID& name() const { return m_name; }
      const FontData& fontData() const { return m_fontData; }
  };

  class LoadMaterialEvent : public EventWithCallback {
    private:
      const StringID m_name;
      MaterialData m_materialData;

    public:
      LoadMaterialEvent(const StringID& name, MaterialData&& materialData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_materialData(std::move(materialData))
      {}

      std::string toString() const override {
        return "LoadMaterialEvent";
      }

      const StringID& name() const { return m_name; }
      const MaterialData& materialData() const { return m_materialData; }
  };

  class LoadPostprocessEffectEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const PostprocessData& m_postprocessData;

    public:
      LoadPostprocessEffectEvent(const StringID& name, const PostprocessData& postprocessData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_postprocessData(postprocessData)
      {}

      std::string toString() const override {
        return "LoadPostprocessEffectEvent";
      }

      const StringID& name() const { return m_name; }
      const std::vector<std::string>& shaderNames() const { return m_postprocessData.shaderNames; }
  };

  class LoadAudioEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const AudioData& m_audioData;

    public:
      LoadAudioEvent(const StringID& name, const AudioData& audioData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_audioData(audioData)
      {}

      std::string toString() const override {
        return "LoadAudioEvent";
      }

      const StringID& name() const { return m_name; }
      const AudioData& audioData() const { return m_audioData; }
  };

  class LoadTerrainEvent : public EventWithCallback {
    private:
      const StringID m_name;
      const TerrainData& m_terrainData;

    public:
      LoadTerrainEvent(const StringID& name, const TerrainData& terrainData, std::optional<std::function<void()>> callback = std::nullopt) :
        EventWithCallback(callback),
        m_name(name),
        m_terrainData(terrainData)
      {}

      std::string toString() const override {
        return "LoadTerrainEvent";
      }

      const StringID& name() const { return m_name; }
      const TerrainData& terrainData() const { return m_terrainData; }
  };
}

#endif
